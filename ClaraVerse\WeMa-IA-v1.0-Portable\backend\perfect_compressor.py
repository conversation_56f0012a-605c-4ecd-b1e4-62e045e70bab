"""
🚀 PERFECT COMPRESSOR RÉVOLUTIONNAIRE - WeMa IA
Compression intelligente des conversations avec préservation du contexte
Optimisé pour maintenir la qualité des réponses LLM
"""

import logging
import json
import time
from typing import List, Dict, Any, Optional, Tuple
import requests
from datetime import datetime

logger = logging.getLogger(__name__)

class PerfectCompressor:
    """
    🚀 COMPRESSEUR PARFAIT RÉVOLUTIONNAIRE
    
    Fonctionnalités:
    1. Compression intelligente par LLM (qwen3-14b-optimized)
    2. Préservation du contexte important
    3. Gestion des documents RAG
    4. Seuils configurables
    5. Métriques de performance
    """

    def __init__(self, lmstudio_url: str = None):
        # � NOUVEAU: SUPPORT LM STUDIO SERVEUR CENTRAL
        if lmstudio_url is None:
            # Essayer d'abord le serveur stable, puis les fallbacks
            test_urls = [
                "http://***********:1234/v1",   # Serveur stable WiFi entreprise (PRIORITÉ)
                "http://***********:1234/v1",   # Serveur central LM Studio (fallback)
                "http://localhost:1234/v1"      # LM Studio local (fallback)
            ]

            self.lmstudio_url = self._find_working_lmstudio_url(test_urls)
            if not self.lmstudio_url:
                # Fallback sur l'adresse stable par défaut
                self.lmstudio_url = "http://***********:1234/v1"
                logger.warning("⚠️ Aucun serveur LM Studio détecté, utilisation de l'adresse stable par défaut")
        else:
            self.lmstudio_url = lmstudio_url

        self.compression_model = "qwen3-14b"  # Modèle 14B sur LM Studio (demandé par utilisateur)
        self.compression_threshold = 20000  # 20K tokens
        self.max_tokens = 35000  # Maximum absolu
        self.target_tokens = 15000  # Cible après compression
        
        # Métriques
        self.compression_stats = {
            "total_compressions": 0,
            "total_tokens_saved": 0,
            "average_compression_ratio": 0.0,
            "last_compression_time": None
        }
        
        logger.info("🚀 Perfect Compressor initialisé - WeMa IA")
        logger.info(f"   � Serveur: {self.lmstudio_url}")
        logger.info(f"   �🎯 Modèle: {self.compression_model}")
        logger.info(f"   📊 Seuil: {self.compression_threshold} tokens")
        logger.info(f"   🎯 Cible: {self.target_tokens} tokens")
        logger.info(f"   ⚠️ Maximum: {self.max_tokens} tokens")

    def _find_working_lmstudio_url(self, urls: List[str]) -> Optional[str]:
        """Trouver la première URL LM Studio qui fonctionne"""
        for url in urls:
            try:
                logger.info(f"🔍 Test connexion LM Studio: {url}")
                response = requests.get(f"{url}/models", timeout=5)
                if response.status_code == 200:
                    models_data = response.json()
                    model_count = len(models_data.get('data', []))
                    logger.info(f"✅ Serveur LM Studio trouvé: {url} ({model_count} modèles)")
                    return url
                else:
                    logger.debug(f"❌ {url} - Status: {response.status_code}")
            except Exception as e:
                logger.debug(f"❌ {url} - Erreur: {e}")

        logger.warning("⚠️ Aucun serveur LM Studio accessible")
        return None

    def should_compress(self, conversation: List[Dict[str, Any]], documents: List[Dict[str, Any]] = None) -> bool:
        """Déterminer si la compression est nécessaire"""
        total_tokens = self._estimate_tokens(conversation, documents)
        
        logger.info(f"📊 Estimation tokens: {total_tokens}")
        
        if total_tokens > self.max_tokens:
            logger.warning(f"⚠️ DÉPASSEMENT MAXIMUM: {total_tokens} > {self.max_tokens}")
            return True
        elif total_tokens > self.compression_threshold:
            logger.info(f"🔄 Compression recommandée: {total_tokens} > {self.compression_threshold}")
            return True
        else:
            logger.info(f"✅ Pas de compression nécessaire: {total_tokens} < {self.compression_threshold}")
            return False

    def compress_conversation(self, conversation: List[Dict[str, Any]], documents: List[Dict[str, Any]] = None) -> Tuple[List[Dict[str, Any]], Dict[str, Any]]:
        """🚀 COMPRESSION RÉVOLUTIONNAIRE DE LA CONVERSATION"""
        start_time = time.time()
        
        if not conversation:
            return conversation, {"compressed": False, "reason": "Conversation vide"}
        
        original_tokens = self._estimate_tokens(conversation, documents)
        logger.info(f"🔄 Début compression: {original_tokens} tokens")
        
        try:
            # Préparer le contexte pour le LLM
            context_for_llm = self._prepare_context_for_compression(conversation, documents)
            
            # Demander au LLM de compresser intelligemment
            compressed_context = self._compress_with_llm(context_for_llm)
            
            # Reconstruire la conversation compressée
            compressed_conversation = self._reconstruct_conversation(compressed_context, conversation)
            
            # Calculer les métriques
            compressed_tokens = self._estimate_tokens(compressed_conversation, documents)
            compression_ratio = (original_tokens - compressed_tokens) / original_tokens if original_tokens > 0 else 0
            processing_time = time.time() - start_time
            
            # Mettre à jour les statistiques
            self._update_stats(original_tokens, compressed_tokens, compression_ratio)
            
            logger.info(f"✅ Compression terminée en {processing_time:.1f}s")
            logger.info(f"   📊 {original_tokens} → {compressed_tokens} tokens")
            logger.info(f"   📉 Ratio: {compression_ratio:.1%}")
            
            return compressed_conversation, {
                "compressed": True,
                "original_tokens": original_tokens,
                "compressed_tokens": compressed_tokens,
                "compression_ratio": compression_ratio,
                "processing_time": processing_time,
                "method": "llm_intelligent"
            }
            
        except Exception as e:
            logger.error(f"❌ Erreur compression: {e}")
            # Fallback: compression simple
            return self._simple_compression_fallback(conversation), {
                "compressed": True,
                "original_tokens": original_tokens,
                "compressed_tokens": self._estimate_tokens(conversation),
                "compression_ratio": 0.3,  # Estimation
                "processing_time": time.time() - start_time,
                "method": "simple_fallback",
                "error": str(e)
            }

    def _estimate_tokens(self, conversation: List[Dict[str, Any]], documents: List[Dict[str, Any]] = None) -> int:
        """Estimation rapide du nombre de tokens"""
        total_chars = 0
        
        # Compter les caractères de la conversation
        for message in conversation:
            if isinstance(message.get('content'), str):
                total_chars += len(message['content'])
            elif isinstance(message.get('content'), list):
                for content_item in message['content']:
                    if isinstance(content_item, dict) and 'text' in content_item:
                        total_chars += len(content_item['text'])
        
        # Compter les caractères des documents
        if documents:
            for doc in documents:
                if 'content' in doc and isinstance(doc['content'], str):
                    total_chars += len(doc['content'])
        
        # Estimation: ~4 caractères par token pour le français
        estimated_tokens = total_chars // 4
        return estimated_tokens

    def _prepare_context_for_compression(self, conversation: List[Dict[str, Any]], documents: List[Dict[str, Any]] = None) -> str:
        """Préparer le contexte pour la compression par LLM"""
        context_parts = []
        
        # Ajouter les documents en premier (priorité haute)
        if documents:
            context_parts.append("=== DOCUMENTS IMPORTANTS ===")
            for i, doc in enumerate(documents):
                if 'content' in doc:
                    context_parts.append(f"Document {i+1}: {doc.get('name', 'Sans nom')}")
                    context_parts.append(doc['content'][:2000])  # Limiter à 2000 chars par doc
                    context_parts.append("")
        
        # Ajouter la conversation
        context_parts.append("=== CONVERSATION ===")
        for message in conversation:
            role = message.get('role', 'unknown')
            content = message.get('content', '')
            
            if isinstance(content, list):
                # Gérer le contenu multi-modal
                text_content = ""
                for item in content:
                    if isinstance(item, dict) and 'text' in item:
                        text_content += item['text'] + " "
                content = text_content.strip()
            
            context_parts.append(f"{role.upper()}: {content}")
        
        return "\n".join(context_parts)

    def _compress_with_llm(self, context: str) -> str:
        """Compression intelligente avec LM Studio (API OpenAI)"""
        system_prompt = """Tu es un expert en compression de contexte. Compresse le contexte en préservant les informations importantes.

RÈGLES:
1. PRÉSERVER: Documents, questions techniques, données chiffrées, noms propres
2. CONDENSER: Salutations répétitives, confirmations, remerciements
3. SYNTHÉTISER: Longues explications en gardant l'essentiel
4. MAINTENIR: Chronologie et logique
5. RÉDUIRE: 30-50% de la taille

INTERDICTIONS ABSOLUES:
- Pas de balises <think> ou </think>
- Pas de commentaires ou explications
- Pas de métadonnées
- Pas de réflexions personnelles

FORMAT DE RÉPONSE: Uniquement le texte compressé, rien d'autre."""

        user_prompt = f"""CONTEXTE À COMPRESSER:
{context}

CONTEXTE COMPRESSÉ (sans balises, sans commentaires):"""

        try:
            response = requests.post(
                f"{self.lmstudio_url}/chat/completions",
                json={
                    "model": self.compression_model,
                    "messages": [
                        {"role": "system", "content": system_prompt},
                        {"role": "user", "content": user_prompt}
                    ],
                    "temperature": 0.1,  # Très déterministe
                    "max_tokens": 4000,  # Limite pour éviter les réponses trop longues
                    "top_p": 0.9,
                    "stream": False
                },
                headers={"Content-Type": "application/json"},
                timeout=120
            )

            if response.status_code == 200:
                result = response.json()
                compressed_text = result.get('choices', [{}])[0].get('message', {}).get('content', '').strip()

                # Nettoyer les balises <think> et autres artefacts (Qwen3 génère toujours ces balises)
                import re

                # Méthode 1: Couper avant <think>
                if '<think>' in compressed_text:
                    compressed_text = compressed_text.split('<think>')[0].strip()

                # Méthode 2: Supprimer toutes balises XML/HTML
                compressed_text = re.sub(r'<[^>]*>', '', compressed_text)

                # Méthode 3: Supprimer lignes qui commencent par des balises
                lines = compressed_text.split('\n')
                clean_lines = []
                for line in lines:
                    line = line.strip()
                    if not line.startswith('<') and not line.endswith('>') and line:
                        clean_lines.append(line)

                if clean_lines:
                    compressed_text = '\n'.join(clean_lines)

                compressed_text = compressed_text.strip()

                if len(compressed_text) > 50:  # Vérification basique
                    return compressed_text
                else:
                    raise ValueError("Compression trop agressive")
            else:
                error_text = response.text
                raise ValueError(f"Erreur API LM Studio: {response.status_code} - {error_text}")

        except Exception as e:
            logger.error(f"❌ Erreur compression LM Studio: {e}")
            raise

    def _reconstruct_conversation(self, compressed_context: str, original_conversation: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Reconstruire la conversation à partir du contexte compressé"""
        # NETTOYER LE CONTEXTE COMPRESSÉ (Qwen3 génère des balises <think>)
        import re

        # Méthode 1: Couper avant <think>
        if '<think>' in compressed_context:
            compressed_context = compressed_context.split('<think>')[0].strip()

        # Méthode 2: Supprimer toutes balises XML/HTML
        compressed_context = re.sub(r'<[^>]*>', '', compressed_context)

        # Méthode 3: Supprimer lignes qui commencent par des balises
        lines = compressed_context.split('\n')
        clean_lines = []
        for line in lines:
            line = line.strip()
            if not line.startswith('<') and not line.endswith('>') and line:
                clean_lines.append(line)

        if clean_lines:
            compressed_context = '\n'.join(clean_lines)

        compressed_context = compressed_context.strip()

        # Garder toujours le dernier message utilisateur et la dernière réponse
        if len(original_conversation) >= 2:
            last_messages = original_conversation[-2:]
        else:
            last_messages = original_conversation

        # Créer un message système avec le contexte compressé NETTOYÉ
        compressed_conversation = [
            {
                "role": "system",
                "content": f"Contexte de conversation précédent (compressé):\n\n{compressed_context}"
            }
        ]

        # Ajouter les derniers messages
        compressed_conversation.extend(last_messages)

        return compressed_conversation

    def _simple_compression_fallback(self, conversation: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Compression simple en cas d'échec du LLM"""
        if len(conversation) <= 3:
            return conversation

        # Garder le premier message (système), les 2 derniers messages
        compressed = []

        # Premier message (souvent système)
        if conversation[0].get('role') == 'system':
            compressed.append(conversation[0])

        # Résumé des messages du milieu
        middle_messages = conversation[1:-2] if len(conversation) > 3 else []
        if middle_messages:
            summary_content = f"[Résumé de {len(middle_messages)} messages précédents - compression automatique]"
            compressed.append({
                "role": "system",
                "content": summary_content
            })

        # Derniers messages
        compressed.extend(conversation[-2:])

        return compressed

    def _update_stats(self, original_tokens: int, compressed_tokens: int, compression_ratio: float):
        """Mettre à jour les statistiques de compression"""
        self.compression_stats["total_compressions"] += 1
        self.compression_stats["total_tokens_saved"] += (original_tokens - compressed_tokens)

        # Moyenne mobile du ratio de compression
        current_avg = self.compression_stats["average_compression_ratio"]
        total_compressions = self.compression_stats["total_compressions"]

        new_avg = ((current_avg * (total_compressions - 1)) + compression_ratio) / total_compressions
        self.compression_stats["average_compression_ratio"] = new_avg
        self.compression_stats["last_compression_time"] = datetime.now().isoformat()

    def get_stats(self) -> Dict[str, Any]:
        """Obtenir les statistiques de compression"""
        return {
            **self.compression_stats,
            "configuration": {
                "compression_threshold": self.compression_threshold,
                "max_tokens": self.max_tokens,
                "target_tokens": self.target_tokens,
                "compression_model": self.compression_model
            }
        }

    def reset_stats(self):
        """Réinitialiser les statistiques"""
        self.compression_stats = {
            "total_compressions": 0,
            "total_tokens_saved": 0,
            "average_compression_ratio": 0.0,
            "last_compression_time": None
        }
        logger.info("📊 Statistiques de compression réinitialisées")

    def update_configuration(self, **kwargs):
        """Mettre à jour la configuration"""
        if 'compression_threshold' in kwargs:
            self.compression_threshold = kwargs['compression_threshold']
            logger.info(f"📊 Seuil de compression mis à jour: {self.compression_threshold}")

        if 'max_tokens' in kwargs:
            self.max_tokens = kwargs['max_tokens']
            logger.info(f"⚠️ Maximum de tokens mis à jour: {self.max_tokens}")

        if 'target_tokens' in kwargs:
            self.target_tokens = kwargs['target_tokens']
            logger.info(f"🎯 Cible de tokens mise à jour: {self.target_tokens}")

        if 'compression_model' in kwargs:
            self.compression_model = kwargs['compression_model']
            logger.info(f"🎯 Modèle de compression mis à jour: {self.compression_model}")

# Fin du fichier Perfect Compressor révolutionnaire - WeMa IA
