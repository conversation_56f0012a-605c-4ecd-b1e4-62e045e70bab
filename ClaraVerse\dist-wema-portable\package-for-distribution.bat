@echo off
echo ========================================
echo    WeMa IA - Packaging Distribution
echo ========================================
echo.

set DIST_NAME=WeMa-IA-v1.0-Portable
set DIST_DIR=..\%DIST_NAME%

echo 🔧 Preparation du package de distribution...

REM Nettoyer le dossier de destination
if exist "%DIST_DIR%" (
    echo 🧹 Nettoyage ancien package...
    rmdir /S /Q "%DIST_DIR%"
)

echo 📁 Creation du package: %DIST_NAME%
mkdir "%DIST_DIR%"

echo 📋 Copie des fichiers essentiels...

REM Copier les dossiers principaux
echo   - Backend...
xcopy "backend" "%DIST_DIR%\backend" /E /I /Q >nul
echo   - Frontend...
xcopy "frontend" "%DIST_DIR%\frontend" /E /I /Q >nul

REM Copier les scripts
echo   - Scripts de demarrage...
copy "start-wema-new.bat" "%DIST_DIR%\" >nul
copy "finalize-wema.bat" "%DIST_DIR%\" >nul
copy "test-wema.bat" "%DIST_DIR%\" >nul

REM Copier la documentation
echo   - Documentation...
copy "README.txt" "%DIST_DIR%\" >nul 2>nul
copy "VERSION.txt" "%DIST_DIR%\" >nul

REM Nettoyer les fichiers temporaires
echo 🧹 Nettoyage des fichiers temporaires...
del /Q "%DIST_DIR%\backend\*.pyc" >nul 2>nul
rmdir /S /Q "%DIST_DIR%\backend\__pycache__" >nul 2>nul
rmdir /S /Q "%DIST_DIR%\backend\lightrag_storage" >nul 2>nul
rmdir /S /Q "%DIST_DIR%\backend\qdrant_storage" >nul 2>nul
del /Q "%DIST_DIR%\backend\documents.db" >nul 2>nul
rmdir /S /Q "%DIST_DIR%\backend\uploads" >nul 2>nul

REM Creer un fichier d'installation
echo 📝 Creation du guide d'installation...
(
echo INSTALLATION WeMa IA v1.0
echo ==========================
echo.
echo 1. Extraire tous les fichiers
echo 2. Double-cliquer sur finalize-wema.bat
echo 3. Double-cliquer sur start-wema-new.bat
echo 4. Ouvrir http://localhost:8080
echo.
echo PREREQUIS:
echo - Python 3.8+ avec pip
echo - Connexion internet
echo.
echo SUPPORT:
echo - Tester avec test-wema.bat
echo - Voir README.txt pour details
) > "%DIST_DIR%\INSTALLATION.txt"

echo.
echo ========================================
echo      ✅ PACKAGE PRET !
echo ========================================
echo.
echo 📁 Dossier: %DIST_NAME%
echo 📦 Contenu:
echo   ✅ Backend Python complet
echo   ✅ Frontend web compile
echo   ✅ Scripts automatiques
echo   ✅ Documentation complete
echo   ✅ Tests automatiques
echo.
echo 🚀 Pret pour distribution !
echo 💾 Vous pouvez maintenant zipper le dossier
echo    %DIST_NAME% pour distribution.
echo.
pause
