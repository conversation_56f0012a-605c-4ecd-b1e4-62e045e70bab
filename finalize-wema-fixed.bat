@echo off
echo ========================================
echo      WeMa IA - Finalisation v1.0
echo ========================================
echo.
echo 🔧 Verification de l'environnement...

REM Verifier Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python non trouve
    echo 📦 Installez Python depuis python.org
    pause
    exit /b 1
)
echo ✅ Python detecte

REM Installer les dependances avec versions fixes
echo 🔧 Installation des dependances (peut prendre quelques minutes)...
cd backend
echo   - Mise a jour pip...
python -m pip install --upgrade pip >nul 2>&1
echo   - NumPy et Pandas (versions compatibles)...
pip install --upgrade numpy==1.26.4 pandas==2.0.3 >nul 2>&1
echo   - FastAPI et serveur web...
pip install --upgrade fastapi uvicorn python-multipart pydantic >nul 2>&1
echo   - OCR et documents...
pip install --upgrade pytesseract Pillow pdf2image opencv-python pymupdf pdfplumber >nul 2>&1
pip install --upgrade pypdf python-docx python-pptx >nul 2>&1
echo   - Utilitaires web...
pip install --upgrade requests aiohttp beautifulsoup4 >nul 2>&1
echo   - Services optionnels...
pip install --upgrade redis aioredis langfuse spacy gtts pyttsx3 >nul 2>&1
pip install --upgrade presidio-analyzer presidio-anonymizer >nul 2>&1
echo   - Modele spaCy...
python -m spacy download en_core_web_sm >nul 2>&1
echo ✅ Dependances installees

REM Test du backend
echo 🔧 Test du backend...
timeout /t 2 /nobreak >nul
python -c "import fastapi, pytesseract, cv2; print('Backend OK')" >nul 2>&1
if errorlevel 1 (
    echo ❌ Erreur backend
    pause
    exit /b 1
)
echo ✅ Backend valide

cd ..

REM Verification frontend
echo 🔧 Verification frontend...
if not exist "frontend\index.html" (
    echo ❌ Frontend manquant
    pause
    exit /b 1
)
echo ✅ Frontend present

echo.
echo ========================================
echo      ✅ WeMa IA PRET !
echo ========================================
echo.
echo 🚀 Pour demarrer: start-wema-new.bat
echo 🌐 Interface: http://localhost:8080
echo 🔧 API: http://localhost:5001
echo.
echo 📋 Fonctionnalites:
echo   ✅ Chat IA avec LM Studio Central
echo   ✅ Recherche Internet (SearXNG Central)
echo   ✅ Upload et OCR de documents
echo   ✅ Interface en francais
echo.
echo Version: 1.0.0 Portable
echo Date: 2025-07-09
echo.
pause
