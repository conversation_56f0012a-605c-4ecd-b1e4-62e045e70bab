# Web framework
fastapi
uvicorn
python-multipart  # For file uploads
pydantic # Using 1.x for better compatibility

# Supprimé : LightRAG, Qdrant, BGE-M3 - Remplacé par mode rapide optimisé

# Document processing
pypdf
python-docx
python-pptx  # PowerPoint support
pandas

# Utilities
requests
aiohttp>=3.8.0  # For async HTTP requests (internet search)
beautifulsoup4>=4.12.0  # For web scraping and content extraction

# Performance & Caching
redis>=4.5.0  # Intelligent caching
aioredis>=2.0.0  # Async Redis support

# 🚀 LLM Observability & Tracing
langfuse>=2.0.0  # Performance monitoring and tracing

# IMPORTANT: Keep numpy locked at a compatible version
numpy

faster-whisper

# diffusers[torch]  # Commenté pour package portable
# torch  # Commenté pour package portable
# transformers  # Commenté pour package portable
# safetensors  # Commenté pour package portable
# accelerate  # Commenté pour package portable

# Text-to-Speech
gtts
pyttsx3

# spaCy for NLP (required by <PERSON>kor<PERSON>/misaki)
spacy>=3.4.0

# Kokoro TTS - Commenté pour package portable
# kokoro>=0.9.4
# kokoro-onnx>=0.4.9
# soundfile>=0.12.1
# misaki[en]>=0.1.0
# onnxruntime>=1.16.0

# GDPR Anonymization
presidio-analyzer>=2.2.0
presidio-anonymizer>=2.2.0

# OCR and Image Processing - Core + Table Support
pytesseract>=0.3.10
Pillow>=10.0.0
pdf2image>=1.17.0
opencv-python>=4.8.0  # Essential for image preprocessing and table detection
pymupdf>=1.23.0  # Essential for PDF text extraction
pdfplumber>=0.10.0  # Essential for table extraction from PDFs
# Optional advanced features
# camelot-py[cv]>=0.11.0  # Advanced table extraction (install manually if needed)
# tabula-py>=2.8.0  # Java-based table extraction (install manually if needed)

# Additional NLP models for Presidio
spacy[en_core_web_sm]