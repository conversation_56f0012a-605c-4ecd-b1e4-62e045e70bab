"""
OCR Service Premium - Service OCR indépendant extrait du GDPR Processor
Traitement OCR avancé pour documents PDF et images avec Tesseract
"""

import os
import tempfile
import logging
import io
from pathlib import Path
from typing import List, Optional, Dict, Any, Tuple
from dataclasses import dataclass
from PIL import Image
import pytesseract
import fitz  # PyMuPDF
import cv2
import numpy as np

# Configuration du logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class TextBlock:
    """Représente un bloc de texte extrait par OCR"""
    text: str
    confidence: float
    bbox: Tuple[int, int, int, int]  # x, y, width, height
    page_number: int

@dataclass
class OcrResult:
    """Résultat du traitement OCR"""
    success: bool
    full_text: str
    text_blocks: List[TextBlock]
    total_pages: int
    processing_time: float
    language: str
    error_message: Optional[str] = None
    confidence: Optional[float] = None

class PremiumOcrProcessor:
    """
    Processeur OCR Premium avec optimisations avancées
    Extrait du GDPR Processor pour utilisation indépendante
    """
    
    def __init__(self):
        self.supported_languages = {
            'fra': 'French',
            'eng': 'English', 
            'deu': 'German',
            'spa': 'Spanish',
            'ita': 'Italian'
        }
        
        # Configuration Tesseract optimisée
        self.tesseract_config = '--oem 3 --psm 6'
        
        # Vérifier que Tesseract est installé
        try:
            pytesseract.get_tesseract_version()
            logger.info("Tesseract OCR détecté et fonctionnel")
        except Exception as e:
            logger.error(f"Erreur Tesseract: {e}")
            raise RuntimeError("Tesseract OCR n'est pas installé ou accessible")

    def _preprocess_image(self, image: Image.Image) -> Image.Image:
        """
        Prétraitement d'image pour améliorer la qualité OCR
        """
        try:
            # Convertir en numpy array
            img_array = np.array(image)
            
            # Convertir en niveaux de gris si nécessaire
            if len(img_array.shape) == 3:
                img_array = cv2.cvtColor(img_array, cv2.COLOR_RGB2GRAY)
            
            # Amélioration du contraste
            img_array = cv2.equalizeHist(img_array)
            
            # Débruitage
            img_array = cv2.medianBlur(img_array, 3)
            
            # Binarisation adaptative
            img_array = cv2.adaptiveThreshold(
                img_array, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
                cv2.THRESH_BINARY, 11, 2
            )
            
            # Reconvertir en PIL Image
            return Image.fromarray(img_array)
            
        except Exception as e:
            logger.warning(f"Erreur prétraitement image: {e}")
            return image

    def _extract_text_from_image(self, image: Image.Image, language: str, page_num: int) -> List[TextBlock]:
        """
        Extrait le texte d'une image avec informations de position
        """
        try:
            # Prétraitement de l'image
            processed_image = self._preprocess_image(image)
            
            # Configuration Tesseract avec langue
            config = f'-l {language} {self.tesseract_config}'
            
            # Extraction avec données détaillées
            data = pytesseract.image_to_data(
                processed_image, 
                config=config, 
                output_type=pytesseract.Output.DICT
            )
            
            text_blocks = []
            current_text = ""
            current_bbox = None
            confidences = []
            
            for i in range(len(data['text'])):
                text = data['text'][i].strip()
                conf = int(data['conf'][i])
                
                if text and conf > 30:  # Seuil de confiance minimum
                    x, y, w, h = data['left'][i], data['top'][i], data['width'][i], data['height'][i]
                    
                    if current_text:
                        current_text += " " + text
                        # Étendre la bbox
                        if current_bbox:
                            current_bbox = (
                                min(current_bbox[0], x),
                                min(current_bbox[1], y),
                                max(current_bbox[0] + current_bbox[2], x + w) - min(current_bbox[0], x),
                                max(current_bbox[1] + current_bbox[3], y + h) - min(current_bbox[1], y)
                            )
                    else:
                        current_text = text
                        current_bbox = (x, y, w, h)
                    
                    confidences.append(conf)
                    
                    # Créer un bloc si on atteint une fin de ligne ou de paragraphe
                    if data['block_num'][i] != data['block_num'][min(i + 1, len(data['text']) - 1)]:
                        if current_text and current_bbox:
                            avg_confidence = sum(confidences) / len(confidences) if confidences else 0
                            text_blocks.append(TextBlock(
                                text=current_text,
                                confidence=avg_confidence,
                                bbox=current_bbox,
                                page_number=page_num
                            ))
                        current_text = ""
                        current_bbox = None
                        confidences = []
            
            # Ajouter le dernier bloc s'il existe
            if current_text and current_bbox:
                avg_confidence = sum(confidences) / len(confidences) if confidences else 0
                text_blocks.append(TextBlock(
                    text=current_text,
                    confidence=avg_confidence,
                    bbox=current_bbox,
                    page_number=page_num
                ))
            
            return text_blocks
            
        except Exception as e:
            logger.error(f"Erreur extraction texte image: {e}")
            return []

    def _pdf_to_images(self, pdf_path: str, dpi: int = 300) -> List[Image.Image]:
        """
        Convertit un PDF en images haute résolution
        """
        try:
            doc = fitz.open(pdf_path)
            images = []
            
            for page_num in range(len(doc)):
                page = doc.load_page(page_num)
                
                # Matrice de transformation pour la résolution
                mat = fitz.Matrix(dpi / 72, dpi / 72)
                pix = page.get_pixmap(matrix=mat)
                
                # Convertir en PIL Image
                img_data = pix.tobytes("ppm")
                image = Image.open(io.BytesIO(img_data))
                images.append(image)
            
            doc.close()
            return images
            
        except Exception as e:
            logger.error(f"Erreur conversion PDF vers images: {e}")
            return []

    async def process_document(
        self, 
        file_path: str, 
        language: str = "fra",
        preserve_layout: bool = True,
        dpi: int = 300
    ) -> OcrResult:
        """
        Traite un document avec OCR premium
        
        Args:
            file_path: Chemin vers le fichier
            language: Code langue pour OCR
            preserve_layout: Préserver la mise en page
            dpi: Résolution pour conversion PDF
            
        Returns:
            OcrResult avec le texte extrait et métadonnées
        """
        import time
        start_time = time.time()
        
        try:
            if not os.path.exists(file_path):
                return OcrResult(
                    success=False,
                    full_text="",
                    text_blocks=[],
                    total_pages=0,
                    processing_time=0,
                    language=language,
                    error_message="Fichier non trouvé"
                )
            
            file_ext = Path(file_path).suffix.lower()
            all_text_blocks = []
            
            if file_ext == '.pdf':
                # Traitement PDF
                images = self._pdf_to_images(file_path, dpi)
                
                for page_num, image in enumerate(images):
                    page_blocks = self._extract_text_from_image(image, language, page_num + 1)
                    all_text_blocks.extend(page_blocks)
                    
            elif file_ext in ['.png', '.jpg', '.jpeg', '.tiff', '.bmp']:
                # Traitement image directe
                image = Image.open(file_path)
                page_blocks = self._extract_text_from_image(image, language, 1)
                all_text_blocks.extend(page_blocks)
                
            else:
                return OcrResult(
                    success=False,
                    full_text="",
                    text_blocks=[],
                    total_pages=0,
                    processing_time=time.time() - start_time,
                    language=language,
                    error_message=f"Format de fichier non supporté: {file_ext}"
                )
            
            # Assembler le texte complet
            if preserve_layout:
                # Trier par page puis par position Y
                all_text_blocks.sort(key=lambda b: (b.page_number, b.bbox[1]))
                full_text = "\n".join([block.text for block in all_text_blocks])
            else:
                full_text = " ".join([block.text for block in all_text_blocks])
            
            # Calculer confiance moyenne
            confidences = [block.confidence for block in all_text_blocks if block.confidence > 0]
            avg_confidence = sum(confidences) / len(confidences) if confidences else 0
            
            total_pages = max([block.page_number for block in all_text_blocks]) if all_text_blocks else 0
            
            processing_time = time.time() - start_time
            
            logger.info(f"OCR terminé: {len(all_text_blocks)} blocs, {len(full_text)} caractères, {processing_time:.2f}s")
            
            return OcrResult(
                success=True,
                full_text=full_text,
                text_blocks=all_text_blocks,
                total_pages=total_pages,
                processing_time=processing_time,
                language=language,
                confidence=avg_confidence
            )
            
        except Exception as e:
            logger.error(f"Erreur traitement OCR: {e}")
            return OcrResult(
                success=False,
                full_text="",
                text_blocks=[],
                total_pages=0,
                processing_time=time.time() - start_time,
                language=language,
                error_message=str(e)
            )

# Instance globale du processeur OCR
ocr_processor = PremiumOcrProcessor()
