"""
🚀 Langfuse Observability Service
Service de traçage et monitoring des performances LLM avec Langfuse
Intégration officielle selon la documentation Langfuse
"""

import os
import time
import json
from typing import Dict, Any, Optional, List
from contextlib import contextmanager
from langfuse import Langfuse
from langfuse.decorators import observe, langfuse_context
import logging

# Configuration du logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class LangfuseService:
    """Service de traçage Langfuse pour monitoring des performances"""

    def __init__(self):
        self.client = None
        self.enabled = False
        self.initialize()

    def initialize(self):
        """Initialise le client Langfuse selon la documentation officielle"""
        try:
            # Configuration depuis les variables d'environnement
            public_key = os.getenv('LANGFUSE_PUBLIC_KEY')
            secret_key = os.getenv('LANGFUSE_SECRET_KEY')
            host = os.getenv('LANGFUSE_HOST', 'https://cloud.langfuse.com')

            if public_key and secret_key:
                # Configuration officielle Langfuse
                langfuse_context.configure(
                    public_key=public_key,
                    secret_key=secret_key,
                    host=host,
                    enabled=True
                )

                self.client = Langfuse(
                    public_key=public_key,
                    secret_key=secret_key,
                    host=host
                )
                self.enabled = True
                logger.info("🚀 Langfuse service initialized successfully")
            else:
                logger.warning("⚠️ Langfuse keys not found - tracing disabled")
                # Mode développement local - désactiver Langfuse
                langfuse_context.configure(enabled=False)
                self.enabled = False

        except Exception as e:
            logger.error(f"❌ Failed to initialize Langfuse: {e}")
            langfuse_context.configure(enabled=False)
            self.enabled = False
    
    @contextmanager
    def trace_operation(self, name: str, metadata: Dict[str, Any] = None):
        """Context manager pour tracer une opération"""
        if not self.enabled:
            yield None
            return
            
        start_time = time.time()
        trace_data = {
            'name': name,
            'start_time': start_time,
            'metadata': metadata or {}
        }
        
        try:
            if hasattr(self.client, 'trace'):
                trace = self.client.trace(name=name, metadata=metadata)
                yield trace
            else:
                yield MockTrace(name, metadata)
        except Exception as e:
            logger.error(f"❌ Tracing error for {name}: {e}")
            yield MockTrace(name, metadata)
        finally:
            duration = time.time() - start_time
            logger.info(f"⏱️ {name} completed in {duration:.3f}s")
    
    def log_llm_call(self, 
                     model: str,
                     input_text: str, 
                     output_text: str,
                     duration: float,
                     tokens_used: Optional[int] = None,
                     metadata: Dict[str, Any] = None):
        """Log un appel LLM"""
        if not self.enabled:
            return
            
        try:
            data = {
                'model': model,
                'input': input_text[:500] + "..." if len(input_text) > 500 else input_text,
                'output': output_text[:500] + "..." if len(output_text) > 500 else output_text,
                'duration': duration,
                'tokens_used': tokens_used,
                'metadata': metadata or {}
            }
            
            logger.info(f"🤖 LLM Call: {model} - {duration:.3f}s - {tokens_used or 'N/A'} tokens")
            
            if hasattr(self.client, 'generation'):
                self.client.generation(
                    name=f"llm_call_{model}",
                    model=model,
                    input=input_text,
                    output=output_text,
                    metadata={
                        'duration': duration,
                        'tokens_used': tokens_used,
                        **(metadata or {})
                    }
                )
        except Exception as e:
            logger.error(f"❌ Failed to log LLM call: {e}")
    
    def log_rag_operation(self,
                         query: str,
                         documents_found: int,
                         response: str,
                         duration: float,
                         metadata: Dict[str, Any] = None):
        """Log une opération RAG"""
        if not self.enabled:
            return
            
        try:
            logger.info(f"🔍 RAG Operation: {documents_found} docs - {duration:.3f}s")
            
            if hasattr(self.client, 'span'):
                self.client.span(
                    name="rag_search",
                    input={'query': query},
                    output={'response': response[:200] + "..." if len(response) > 200 else response},
                    metadata={
                        'documents_found': documents_found,
                        'duration': duration,
                        **(metadata or {})
                    }
                )
        except Exception as e:
            logger.error(f"❌ Failed to log RAG operation: {e}")
    
    def log_performance_metrics(self, operation: str, metrics: Dict[str, Any]):
        """Log des métriques de performance"""
        if not self.enabled:
            return
            
        try:
            logger.info(f"📊 Performance Metrics for {operation}: {metrics}")
            
            if hasattr(self.client, 'score'):
                for metric_name, value in metrics.items():
                    if isinstance(value, (int, float)):
                        self.client.score(
                            name=f"{operation}_{metric_name}",
                            value=value
                        )
        except Exception as e:
            logger.error(f"❌ Failed to log performance metrics: {e}")
    
    def flush(self):
        """Force l'envoi des données en attente"""
        if self.enabled and hasattr(self.client, 'flush'):
            try:
                self.client.flush()
                logger.info("✅ Langfuse data flushed")
            except Exception as e:
                logger.error(f"❌ Failed to flush Langfuse data: {e}")


class MockLangfuseClient:
    """Client mock pour le développement local"""
    
    def trace(self, name: str, metadata: Dict[str, Any] = None):
        return MockTrace(name, metadata)
    
    def generation(self, **kwargs):
        logger.info(f"🤖 [MOCK] LLM Generation: {kwargs.get('model', 'unknown')}")
    
    def span(self, **kwargs):
        logger.info(f"🔍 [MOCK] Span: {kwargs.get('name', 'unknown')}")
    
    def score(self, **kwargs):
        logger.info(f"📊 [MOCK] Score: {kwargs.get('name', 'unknown')} = {kwargs.get('value', 'N/A')}")
    
    def flush(self):
        logger.info("✅ [MOCK] Flush completed")


class MockTrace:
    """Trace mock pour le développement local"""
    
    def __init__(self, name: str, metadata: Dict[str, Any] = None):
        self.name = name
        self.metadata = metadata or {}
        logger.info(f"🔍 [MOCK] Trace started: {name}")
    
    def span(self, **kwargs):
        logger.info(f"📝 [MOCK] Span in {self.name}: {kwargs.get('name', 'unknown')}")
        return MockTrace(kwargs.get('name', 'unknown'))


# Instance globale du service
langfuse_service = LangfuseService()

# 🚀 Décorateurs officiels Langfuse pour traçage automatique

# Décorateur pour les appels LLM (utilise l'API officielle Langfuse)
def trace_llm_call(model_name: str = "unknown"):
    """Décorateur pour tracer automatiquement les appels LLM avec Langfuse"""
    def decorator(func):
        @observe(as_type="generation")
        def wrapper(*args, **kwargs):
            # Mise à jour des métadonnées de l'observation courante
            langfuse_context.update_current_observation(
                name=f"LLM Call: {model_name}",
                model=model_name,
                metadata={
                    "function": func.__name__,
                    "args_count": len(args),
                    "kwargs_keys": list(kwargs.keys())
                }
            )

            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                duration = time.time() - start_time

                # Mise à jour avec les résultats
                langfuse_context.update_current_observation(
                    metadata={
                        "duration_seconds": duration,
                        "success": True
                    }
                )

                logger.info(f"🤖 LLM Call completed: {model_name} - {duration:.3f}s")
                return result

            except Exception as e:
                duration = time.time() - start_time
                langfuse_context.update_current_observation(
                    level="ERROR",
                    metadata={
                        "duration_seconds": duration,
                        "success": False,
                        "error": str(e)
                    }
                )
                logger.error(f"❌ LLM call failed: {model_name} - {duration:.3f}s - {e}")
                raise

        return wrapper
    return decorator

# Décorateur pour les opérations RAG
def trace_rag_operation(operation_name: str = "RAG Search"):
    """Décorateur pour tracer automatiquement les opérations RAG avec Langfuse"""
    def decorator(func):
        @observe(name=operation_name)
        def wrapper(*args, **kwargs):
            # Extraire la query si possible
            query = kwargs.get('query', args[0] if args else 'unknown')

            langfuse_context.update_current_observation(
                input={"query": str(query)[:200]},
                metadata={
                    "function": func.__name__,
                    "operation": operation_name
                }
            )

            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                duration = time.time() - start_time

                # Extraire les informations du résultat
                docs_count = 0
                if isinstance(result, dict):
                    docs_count = len(result.get('vector_results', []))
                    response_preview = str(result.get('combined_response', ''))[:200]
                else:
                    response_preview = str(result)[:200]

                langfuse_context.update_current_observation(
                    output={"response_preview": response_preview},
                    metadata={
                        "duration_seconds": duration,
                        "documents_found": docs_count,
                        "success": True
                    }
                )

                logger.info(f"🔍 RAG Operation completed: {docs_count} docs - {duration:.3f}s")
                return result

            except Exception as e:
                duration = time.time() - start_time
                langfuse_context.update_current_observation(
                    level="ERROR",
                    metadata={
                        "duration_seconds": duration,
                        "success": False,
                        "error": str(e)
                    }
                )
                logger.error(f"❌ RAG operation failed: {duration:.3f}s - {e}")
                raise

        return wrapper
    return decorator
