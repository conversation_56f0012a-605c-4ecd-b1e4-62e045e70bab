"""
🚀 Service de démarrage automatique pour WeMa IA
Gère le démarrage automatique de Docker, SearXNG et tous les services
"""

import os
import sys
import time
import asyncio
import logging
import subprocess
import platform
from pathlib import Path
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)

class StartupService:
    """Service de démarrage automatique pour WeMa IA"""
    
    def __init__(self):
        self.is_windows = platform.system() == "Windows"
        self.project_root = Path(__file__).parent.parent.parent
        self.searxng_path = self.project_root / "searxng"
        self.searxng_portable_path = self.project_root / "searxng-portable"
        self.use_portable_searxng = True  # Priorité au SearXNG portable
        self.startup_status = {
            "docker": False,
            "searxng": False,
            "searxng_portable": False,
            "search_service": False
        }
    
    async def start_all_services(self) -> Dict[str, Any]:
        """Démarrer tous les services automatiquement"""
        logger.info("🚀 Démarrage automatique des services WeMa IA...")
        
        results = {
            "success": True,
            "services": {},
            "errors": []
        }
        
        try:
            # 1. Priorité au SearXNG Portable (sans Docker)
            if self.use_portable_searxng:
                searxng_portable_result = await self._ensure_searxng_portable_running()
                results["services"]["searxng_portable"] = searxng_portable_result

                if searxng_portable_result["status"] == "running":
                    self.startup_status["searxng_portable"] = True
                    logger.info("✅ SearXNG Portable démarré avec succès")
                else:
                    logger.warning("⚠️ SearXNG Portable échoué, fallback vers Docker")
                    self.use_portable_searxng = False

            # 2. Fallback: Vérifier et démarrer Docker + SearXNG
            if not self.use_portable_searxng:
                docker_result = await self._ensure_docker_running()
                results["services"]["docker"] = docker_result

                if docker_result["status"] == "running":
                    # Démarrer SearXNG via Docker
                    searxng_result = await self._ensure_searxng_running()
                    results["services"]["searxng"] = searxng_result
                
                # 3. Vérifier le service de recherche
                search_result = await self._verify_search_service()
                results["services"]["search"] = search_result
            else:
                results["services"]["searxng"] = {"status": "skipped", "reason": "Docker non disponible"}
                results["services"]["search"] = {"status": "fallback", "reason": "SearXNG non disponible"}
        
        except Exception as e:
            logger.error(f"❌ Erreur lors du démarrage automatique: {e}")
            results["success"] = False
            results["errors"].append(str(e))
        
        # Résumé
        self._log_startup_summary(results)
        return results
    
    async def _ensure_docker_running(self) -> Dict[str, Any]:
        """S'assurer que Docker est démarré"""
        logger.info("🐳 Vérification de Docker...")
        
        try:
            # Vérifier si Docker est déjà en cours d'exécution
            result = subprocess.run(
                ["docker", "version", "--format", "json"],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if result.returncode == 0:
                logger.info("✅ Docker est déjà en cours d'exécution")
                self.startup_status["docker"] = True
                return {"status": "running", "message": "Docker déjà actif"}
            
        except (subprocess.TimeoutExpired, FileNotFoundError, subprocess.SubprocessError):
            pass
        
        # Essayer de démarrer Docker Desktop automatiquement
        if self.is_windows:
            return await self._start_docker_windows()
        else:
            return await self._start_docker_unix()
    
    async def _start_docker_windows(self) -> Dict[str, Any]:
        """Démarrer Docker Desktop sur Windows"""
        logger.info("🐳 Tentative de démarrage de Docker Desktop...")
        
        try:
            # Chemins possibles pour Docker Desktop
            docker_paths = [
                r"C:\Program Files\Docker\Docker\Docker Desktop.exe",
                r"C:\Program Files (x86)\Docker\Docker\Docker Desktop.exe",
                os.path.expanduser(r"~\AppData\Local\Docker\Docker Desktop.exe")
            ]
            
            docker_exe = None
            for path in docker_paths:
                if os.path.exists(path):
                    docker_exe = path
                    break
            
            if not docker_exe:
                logger.warning("⚠️ Docker Desktop non trouvé, installation manuelle requise")
                return {"status": "not_found", "message": "Docker Desktop non installé"}
            
            # Démarrer Docker Desktop
            subprocess.Popen([docker_exe], shell=True)
            logger.info("🐳 Docker Desktop en cours de démarrage...")
            
            # Attendre que Docker soit prêt (max 60 secondes)
            for attempt in range(12):  # 12 * 5 = 60 secondes
                await asyncio.sleep(5)
                try:
                    result = subprocess.run(
                        ["docker", "version"],
                        capture_output=True,
                        text=True,
                        timeout=5
                    )
                    if result.returncode == 0:
                        logger.info("✅ Docker Desktop démarré avec succès")
                        self.startup_status["docker"] = True
                        return {"status": "running", "message": "Docker Desktop démarré automatiquement"}
                except:
                    continue
            
            logger.warning("⚠️ Docker Desktop prend plus de temps que prévu à démarrer")
            return {"status": "starting", "message": "Docker en cours de démarrage (peut prendre quelques minutes)"}
            
        except Exception as e:
            logger.error(f"❌ Erreur lors du démarrage de Docker: {e}")
            return {"status": "error", "message": f"Erreur: {str(e)}"}
    
    async def _start_docker_unix(self) -> Dict[str, Any]:
        """Démarrer Docker sur Unix/Linux/Mac"""
        logger.info("🐳 Tentative de démarrage du service Docker...")
        
        try:
            # Essayer de démarrer le service Docker
            result = subprocess.run(
                ["sudo", "systemctl", "start", "docker"],
                capture_output=True,
                text=True,
                timeout=30
            )
            
            if result.returncode == 0:
                # Attendre que Docker soit prêt
                await asyncio.sleep(5)
                
                # Vérifier que Docker fonctionne
                check_result = subprocess.run(
                    ["docker", "version"],
                    capture_output=True,
                    text=True,
                    timeout=10
                )
                
                if check_result.returncode == 0:
                    logger.info("✅ Docker démarré avec succès")
                    self.startup_status["docker"] = True
                    return {"status": "running", "message": "Docker démarré automatiquement"}
            
            logger.warning("⚠️ Impossible de démarrer Docker automatiquement")
            return {"status": "manual_required", "message": "Démarrage manuel de Docker requis"}
            
        except Exception as e:
            logger.error(f"❌ Erreur lors du démarrage de Docker: {e}")
            return {"status": "error", "message": f"Erreur: {str(e)}"}
    
    async def _ensure_searxng_running(self) -> Dict[str, Any]:
        """S'assurer que SearXNG est démarré"""
        logger.info("🔍 Vérification de SearXNG...")
        
        try:
            # Vérifier si SearXNG est déjà en cours d'exécution
            result = subprocess.run(
                ["docker", "ps", "--filter", "name=wema-searxng", "--format", "{{.Status}}"],
                capture_output=True,
                text=True,
                timeout=10,
                cwd=self.searxng_path
            )
            
            if result.returncode == 0 and "Up" in result.stdout:
                logger.info("✅ SearXNG est déjà en cours d'exécution")
                self.startup_status["searxng"] = True
                return {"status": "running", "message": "SearXNG déjà actif"}
            
            # Démarrer SearXNG
            logger.info("🔍 Démarrage de SearXNG...")
            result = subprocess.run(
                ["docker-compose", "up", "-d"],
                capture_output=True,
                text=True,
                timeout=60,
                cwd=self.searxng_path
            )
            
            if result.returncode == 0:
                # Attendre que SearXNG soit prêt
                await asyncio.sleep(10)
                
                # Vérifier que SearXNG répond
                import aiohttp
                async with aiohttp.ClientSession() as session:
                    try:
                        async with session.get("http://localhost:8888", timeout=aiohttp.ClientTimeout(total=10)) as response:
                            if response.status == 200:
                                logger.info("✅ SearXNG démarré avec succès")
                                self.startup_status["searxng"] = True
                                return {"status": "running", "message": "SearXNG démarré automatiquement"}
                    except:
                        pass
                
                logger.warning("⚠️ SearXNG démarré mais pas encore prêt")
                return {"status": "starting", "message": "SearXNG en cours d'initialisation"}
            else:
                logger.error(f"❌ Erreur lors du démarrage de SearXNG: {result.stderr}")
                return {"status": "error", "message": f"Erreur Docker: {result.stderr}"}
                
        except Exception as e:
            logger.error(f"❌ Erreur lors du démarrage de SearXNG: {e}")
            return {"status": "error", "message": f"Erreur: {str(e)}"}
    
    async def _verify_search_service(self) -> Dict[str, Any]:
        """Vérifier que le service de recherche fonctionne"""
        logger.info("🔍 Vérification du service de recherche...")
        
        try:
            from services.internet_search_service import InternetSearchService
            
            async with InternetSearchService() as service:
                is_available = await service.check_availability()
                
            if is_available:
                logger.info("✅ Service de recherche internet opérationnel")
                self.startup_status["search_service"] = True
                return {"status": "operational", "message": "Recherche internet disponible"}
            else:
                logger.info("⚠️ Service de recherche en mode fallback")
                return {"status": "fallback", "message": "Mode fallback activé (résultats d'exemple)"}
                
        except Exception as e:
            logger.error(f"❌ Erreur lors de la vérification du service de recherche: {e}")
            return {"status": "error", "message": f"Erreur: {str(e)}"}
    
    def _log_startup_summary(self, results: Dict[str, Any]):
        """Afficher un résumé du démarrage"""
        logger.info("=" * 60)
        logger.info("🚀 RÉSUMÉ DU DÉMARRAGE AUTOMATIQUE")
        logger.info("=" * 60)
        
        for service_name, service_result in results["services"].items():
            status = service_result.get("status", "unknown")
            message = service_result.get("message", "")
            
            if status == "running" or status == "operational":
                logger.info(f"✅ {service_name.upper()}: {message}")
            elif status == "starting":
                logger.info(f"🔄 {service_name.upper()}: {message}")
            elif status == "fallback":
                logger.info(f"⚠️ {service_name.upper()}: {message}")
            else:
                logger.warning(f"❌ {service_name.upper()}: {message}")
        
        if results["success"]:
            logger.info("🎉 WeMa IA prêt à l'utilisation !")
        else:
            logger.warning("⚠️ Démarrage partiel - certains services peuvent être indisponibles")
        
        logger.info("=" * 60)
    
    def get_status(self) -> Dict[str, Any]:
        """Obtenir le statut des services"""
        return {
            "docker": self.startup_status["docker"],
            "searxng": self.startup_status["searxng"],
            "searxng_portable": self.startup_status.get("searxng_portable", False),
            "search_service": self.startup_status["search_service"]
        }

    async def _ensure_searxng_portable_running(self) -> Dict[str, Any]:
        """Démarrer SearXNG en mode portable (sans Docker)"""
        try:
            import requests

            # Vérifier si SearXNG portable tourne déjà
            try:
                response = requests.get("http://localhost:8888", timeout=3)
                if response.status_code == 200:
                    return {
                        "status": "running",
                        "message": "SearXNG Portable déjà actif",
                        "url": "http://localhost:8888"
                    }
            except:
                pass

            # Vérifier si le dossier portable existe
            if not self.searxng_portable_path.exists():
                return {
                    "status": "error",
                    "message": f"Dossier SearXNG portable introuvable: {self.searxng_portable_path}",
                    "url": None
                }

            # Script de démarrage
            start_script = self.searxng_portable_path / "start-searxng.py"
            if not start_script.exists():
                return {
                    "status": "error",
                    "message": f"Script de démarrage introuvable: {start_script}",
                    "url": None
                }

            logger.info("🚀 Démarrage de SearXNG Portable...")

            # Démarrer SearXNG portable en arrière-plan
            if self.is_windows:
                # Windows: démarrage en arrière-plan
                subprocess.Popen([
                    "python", str(start_script)
                ],
                cwd=str(self.searxng_portable_path),
                creationflags=subprocess.CREATE_NO_WINDOW
                )
            else:
                # Linux/Mac: démarrage en arrière-plan
                subprocess.Popen([
                    "python3", str(start_script)
                ],
                cwd=str(self.searxng_portable_path),
                stdout=subprocess.DEVNULL,
                stderr=subprocess.DEVNULL
                )

            # Attendre que SearXNG démarre (max 30 secondes)
            for i in range(30):
                try:
                    await asyncio.sleep(1)
                    response = requests.get("http://localhost:8888", timeout=2)
                    if response.status_code == 200:
                        logger.info("✅ SearXNG Portable démarré avec succès")
                        return {
                            "status": "running",
                            "message": "SearXNG Portable opérationnel",
                            "url": "http://localhost:8888"
                        }
                except:
                    continue

            # Timeout
            return {
                "status": "error",
                "message": "Timeout - SearXNG Portable n'a pas démarré dans les temps",
                "url": None
            }

        except Exception as e:
            logger.error(f"❌ Erreur démarrage SearXNG Portable: {e}")
            return {
                "status": "error",
                "message": f"Erreur: {str(e)}",
                "url": None
            }

# Instance globale
startup_service = StartupService()
