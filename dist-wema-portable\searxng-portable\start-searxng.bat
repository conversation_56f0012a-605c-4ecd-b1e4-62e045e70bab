@echo off
REM 🔍 SearXNG Portable - Démarrage Windows
REM Version standalone pour WeMa IA

echo 🔍 SearXNG Portable pour WeMa IA
echo =====================================

REM Vérifier Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python n'est pas installé ou accessible
    echo 📥 Veuillez installer Python 3.8+ depuis python.org
    pause
    exit /b 1
)

REM Démarrer SearXNG
echo 🚀 Démarrage de SearXNG...
python start-searxng.py

pause
