@echo off
echo ========================================
echo    WeMa IA - Installation Dependances
echo ========================================
echo.

echo 🔧 Installation des dependances Python...
echo ⚠️ Cela peut prendre quelques minutes...
echo.

cd backend

echo 📦 Mise a jour pip...
python -m pip install --upgrade pip

echo 📦 Installation des dependances principales...
pip install --upgrade numpy==1.26.4
pip install --upgrade pandas==2.0.3
pip install --upgrade fastapi uvicorn python-multipart pydantic

echo 📦 Installation OCR et traitement documents...
pip install --upgrade pytesseract Pillow pdf2image opencv-python pymupdf pdfplumber
pip install --upgrade pypdf python-docx python-pptx

echo 📦 Installation utilitaires web...
pip install --upgrade requests aiohttp beautifulsoup4

echo 📦 Installation services optionnels...
pip install --upgrade redis aioredis langfuse spacy gtts pyttsx3
pip install --upgrade presidio-analyzer presidio-anonymizer

echo 📦 Installation modele spaCy...
python -m spacy download en_core_web_sm

echo.
echo ========================================
echo      ✅ INSTALLATION TERMINEE
echo ========================================
echo.
echo 🎯 Toutes les dependances sont installees !
echo 🚀 Vous pouvez maintenant lancer WeMa IA avec:
echo    start-wema-new.bat
echo.
pause
