function H(n){return Math.abs(n=Math.round(n))>=1e21?n.toLocaleString("en").replace(/,/g,""):n.toString(10)}function j(n,t){if((e=(n=t?n.toExponential(t-1):n.toExponential()).indexOf("e"))<0)return null;var e,i=n.slice(0,e);return[i.length>1?i[0]+i.slice(2):i,+n.slice(e+1)]}function J(n){return n=j(Math.abs(n)),n?n[1]:NaN}function K(n,t){return function(e,i){for(var o=e.length,f=[],c=0,u=n[0],p=0;o>0&&u>0&&(p+u+1>i&&(u=Math.max(1,i-p)),f.push(e.substring(o-=u,o+u)),!((p+=u+1)>i));)u=n[c=(c+1)%n.length];return f.reverse().join(t)}}function Q(n){return function(t){return t.replace(/[0-9]/g,function(e){return n[+e]})}}var V=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function N(n){if(!(t=V.exec(n)))throw new Error("invalid format: "+n);var t;return new $({fill:t[1],align:t[2],sign:t[3],symbol:t[4],zero:t[5],width:t[6],comma:t[7],precision:t[8]&&t[8].slice(1),trim:t[9],type:t[10]})}N.prototype=$.prototype;function $(n){this.fill=n.fill===void 0?" ":n.fill+"",this.align=n.align===void 0?">":n.align+"",this.sign=n.sign===void 0?"-":n.sign+"",this.symbol=n.symbol===void 0?"":n.symbol+"",this.zero=!!n.zero,this.width=n.width===void 0?void 0:+n.width,this.comma=!!n.comma,this.precision=n.precision===void 0?void 0:+n.precision,this.trim=!!n.trim,this.type=n.type===void 0?"":n.type+""}$.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(this.width===void 0?"":Math.max(1,this.width|0))+(this.comma?",":"")+(this.precision===void 0?"":"."+Math.max(0,this.precision|0))+(this.trim?"~":"")+this.type};function W(n){n:for(var t=n.length,e=1,i=-1,o;e<t;++e)switch(n[e]){case".":i=o=e;break;case"0":i===0&&(i=e),o=e;break;default:if(!+n[e])break n;i>0&&(i=0);break}return i>0?n.slice(0,i)+n.slice(o+1):n}var U;function _(n,t){var e=j(n,t);if(!e)return n+"";var i=e[0],o=e[1],f=o-(U=Math.max(-8,Math.min(8,Math.floor(o/3)))*3)+1,c=i.length;return f===c?i:f>c?i+new Array(f-c+1).join("0"):f>0?i.slice(0,f)+"."+i.slice(f):"0."+new Array(1-f).join("0")+j(n,Math.max(0,t+f-1))[0]}function G(n,t){var e=j(n,t);if(!e)return n+"";var i=e[0],o=e[1];return o<0?"0."+new Array(-o).join("0")+i:i.length>o+1?i.slice(0,o+1)+"."+i.slice(o+1):i+new Array(o-i.length+2).join("0")}const I={"%":(n,t)=>(n*100).toFixed(t),b:n=>Math.round(n).toString(2),c:n=>n+"",d:H,e:(n,t)=>n.toExponential(t),f:(n,t)=>n.toFixed(t),g:(n,t)=>n.toPrecision(t),o:n=>Math.round(n).toString(8),p:(n,t)=>G(n*100,t),r:G,s:_,X:n=>Math.round(n).toString(16).toUpperCase(),x:n=>Math.round(n).toString(16)};function X(n){return n}var O=Array.prototype.map,R=["y","z","a","f","p","n","µ","m","","k","M","G","T","P","E","Z","Y"];function v(n){var t=n.grouping===void 0||n.thousands===void 0?X:K(O.call(n.grouping,Number),n.thousands+""),e=n.currency===void 0?"":n.currency[0]+"",i=n.currency===void 0?"":n.currency[1]+"",o=n.decimal===void 0?".":n.decimal+"",f=n.numerals===void 0?X:Q(O.call(n.numerals,String)),c=n.percent===void 0?"%":n.percent+"",u=n.minus===void 0?"−":n.minus+"",p=n.nan===void 0?"NaN":n.nan+"";function F(a){a=N(a);var x=a.fill,M=a.align,m=a.sign,w=a.symbol,l=a.zero,S=a.width,E=a.comma,g=a.precision,L=a.trim,d=a.type;d==="n"?(E=!0,d="g"):I[d]||(g===void 0&&(g=12),L=!0,d="g"),(l||x==="0"&&M==="=")&&(l=!0,x="0",M="=");var Z=w==="$"?e:w==="#"&&/[boxX]/.test(d)?"0"+d.toLowerCase():"",q=w==="$"?i:/[%p]/.test(d)?c:"",T=I[d],B=/[defgprs%]/.test(d);g=g===void 0?6:/[gprs]/.test(d)?Math.max(1,Math.min(21,g)):Math.max(0,Math.min(20,g));function C(r){var y=Z,h=q,b,D,k;if(d==="c")h=T(r)+h,r="";else{r=+r;var P=r<0||1/r<0;if(r=isNaN(r)?p:T(Math.abs(r),g),L&&(r=W(r)),P&&+r==0&&m!=="+"&&(P=!1),y=(P?m==="("?m:u:m==="-"||m==="("?"":m)+y,h=(d==="s"?R[8+U/3]:"")+h+(P&&m==="("?")":""),B){for(b=-1,D=r.length;++b<D;)if(k=r.charCodeAt(b),48>k||k>57){h=(k===46?o+r.slice(b+1):r.slice(b))+h,r=r.slice(0,b);break}}}E&&!l&&(r=t(r,1/0));var z=y.length+r.length+h.length,s=z<S?new Array(S-z+1).join(x):"";switch(E&&l&&(r=t(s+r,s.length?S-h.length:1/0),s=""),M){case"<":r=y+r+h+s;break;case"=":r=y+s+r+h;break;case"^":r=s.slice(0,z=s.length>>1)+y+r+h+s.slice(z);break;default:r=s+y+r+h;break}return f(r)}return C.toString=function(){return a+""},C}function Y(a,x){var M=F((a=N(a),a.type="f",a)),m=Math.max(-8,Math.min(8,Math.floor(J(x)/3)))*3,w=Math.pow(10,-m),l=R[8+m/3];return function(S){return M(w*S)+l}}return{format:F,formatPrefix:Y}}var A,nn,tn;rn({thousands:",",grouping:[3],currency:["$",""]});function rn(n){return A=v(n),nn=A.format,tn=A.formatPrefix,A}export{tn as a,nn as b,J as e,N as f};
